import { LetterTemplate } from './letter-templates/applicationLetterTemplates';
import { StructuredLetterData, LetterTemplateData, convertToLetterTemplateData, validateLetterTemplateData } from '../types/letter-structured';

/**
 * Template compilation cache for performance
 * Key: template ID, Value: precompiled Handlebars template
 */
const precompiledLetterTemplates = new Map<string, any>();

/**
 * Initialize precompiled templates
 * This should be called once at application startup
 */
export function preCompileLetterTemplates(): void {
  // Note: We'll implement this after converting templates to Handlebars format
  console.log('Letter template precompilation will be implemented after template conversion');
}

/**
 * Clear template cache (useful for development/testing)
 */
export function clearLetterTemplateCache(): void {
  precompiledLetterTemplates.clear();
}

/**
 * Get or compile a template
 * @param template - Letter template object
 * @returns Compiled Handlebars template function
 */
function getCompiledLetterTemplate(template: LetterTemplate): any {
  // Check cache first
  if (precompiledLetterTemplates.has(template.id)) {
    return precompiledLetterTemplates.get(template.id);
  }

  // For now, we'll use a simple string replacement approach
  // This will be replaced with actual Handlebars compilation after template conversion
  const compiledTemplate = (data: LetterTemplateData) => {
    let html = template.templateHtml;
    
    // Replace basic placeholders
    html = html.replace(/\{\{name\}\}/g, data.name || '');
    html = html.replace(/\{\{email\}\}/g, data.email || '');
    html = html.replace(/\{\{phone\}\}/g, data.phone || '');
    html = html.replace(/\{\{location\}\}/g, data.location || '');
    html = html.replace(/\{\{position\}\}/g, data.position || '');
    html = html.replace(/\{\{company\}\}/g, data.company || '');
    html = html.replace(/\{\{date\}\}/g, data.date || '');
    html = html.replace(/\{\{subject\}\}/g, data.subject || '');
    html = html.replace(/\{\{salutation\}\}/g, data.salutation || '');
    html = html.replace(/\{\{opening\}\}/g, data.opening || '');
    html = html.replace(/\{\{closing\}\}/g, data.closing || '');
    html = html.replace(/\{\{signature\}\}/g, data.signature || '');

    // Handle body paragraphs - detect template style
    if (data.bodyParagraphs && data.bodyParagraphs.length > 0) {
      let bodyHtml;

      // Check if template uses space-y-5 class (classic blue style)
      if (html.includes('space-y-5')) {
        bodyHtml = data.bodyParagraphs
          .map(paragraph => `<p>${paragraph}</p>`)
          .join('\n                   ');
      } else {
        // Default plain text style
        bodyHtml = data.bodyParagraphs
          .map(paragraph => `<div class="body-text"><p>${paragraph}</p></div>`)
          .join('\n                ');
      }

      html = html.replace(/\{\{bodyParagraphs\}\}/g, bodyHtml);
    }

    // Handle optional contact info
    if (data.linkedin) {
      html = html.replace(/\{\{#if linkedin\}\}([\s\S]*?)\{\{\/if\}\}/g, '$1');
      html = html.replace(/\{\{linkedin\}\}/g, data.linkedin);
    } else {
      html = html.replace(/\{\{#if linkedin\}\}([\s\S]*?)\{\{\/if\}\}/g, '');
    }

    if (data.website) {
      html = html.replace(/\{\{#if website\}\}([\s\S]*?)\{\{\/if\}\}/g, '$1');
      html = html.replace(/\{\{website\}\}/g, data.website);
    } else {
      html = html.replace(/\{\{#if website\}\}([\s\S]*?)\{\{\/if\}\}/g, '');
    }

    if (data.github) {
      html = html.replace(/\{\{#if github\}\}([\s\S]*?)\{\{\/if\}\}/g, '$1');
      html = html.replace(/\{\{github\}\}/g, data.github);
    } else {
      html = html.replace(/\{\{#if github\}\}([\s\S]*?)\{\{\/if\}\}/g, '');
    }

    return html;
  };

  // Cache the compiled template
  precompiledLetterTemplates.set(template.id, compiledTemplate);
  
  return compiledTemplate;
}

/**
 * Validate template data and provide detailed error information
 * @param templateData - Template data to validate
 * @returns Validation result with details
 */
function validateLetterTemplateDataWithDetails(templateData: LetterTemplateData): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Validate required fields
  const validation = validateLetterTemplateData(templateData);
  if (!validation.isValid) {
    errors.push(...validation.missingFields.map(field => `Missing required field: ${field}`));
  }
  
  if (validation.warnings) {
    warnings.push(...validation.warnings);
  }
  
  return { isValid: validation.isValid, errors, warnings };
}

/**
 * Main function to fill letter template with structured data
 * @param template - Letter template object
 * @param data - Structured letter data
 * @returns Filled HTML string
 * @throws Error if template compilation fails or data validation fails
 */
export function fillLetterTemplate(template: LetterTemplate, data: StructuredLetterData): string {
  try {
    // Convert structured data to template format
    const templateData = convertToLetterTemplateData(data);
    
    // Validate template data
    const validation = validateLetterTemplateDataWithDetails(templateData);
    
    if (!validation.isValid) {
      throw new Error(`Letter template data validation failed: ${validation.errors.join(', ')}`);
    }
    
    // Log warnings if any
    if (validation.warnings.length > 0) {
      console.warn('Letter template warnings:', validation.warnings);
    }
    
    // Get compiled template
    const compiledTemplate = getCompiledLetterTemplate(template);
    
    // Fill template with data
    const filledHtml = compiledTemplate(templateData);
    
    // Basic validation of output
    if (!filledHtml || filledHtml.trim().length === 0) {
      throw new Error('Template compilation resulted in empty output');
    }
    
    // Ensure it looks like HTML
    if (!filledHtml.includes('<') || !filledHtml.includes('>')) {
      throw new Error('Template compilation did not produce valid HTML');
    }
    
    return filledHtml;
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    // Log error for debugging with more context
    console.error('Letter template fill error:', {
      templateId: template.id,
      templateName: template.name,
      error: errorMessage,
      applicantName: data?.personalInfo?.fullName || 'Unknown',
      position: data?.jobInfo?.position || 'Unknown',
      company: data?.jobInfo?.company || 'Unknown',
      dataStructure: {
        hasPersonalInfo: !!data?.personalInfo,
        hasJobInfo: !!data?.jobInfo,
        hasLetterContent: !!data?.letterContent,
        bodyParagraphsCount: data?.letterContent?.body?.length || 0,
        hasExperience: !!data?.relevantExperience && Array.isArray(data.relevantExperience),
        experienceCount: data?.relevantExperience?.length || 0,
        hasSkills: !!data?.relevantSkills && Array.isArray(data.relevantSkills),
        skillsCount: data?.relevantSkills?.length || 0,
      }
    });
    
    // Re-throw with more context
    throw new Error(`Failed to fill letter template "${template.name}": ${errorMessage}`);
  }
}

/**
 * Test letter template compilation with sample data
 * @param template - Template to test
 * @param sampleData - Sample structured letter data
 * @returns Test result
 */
export function testLetterTemplate(template: LetterTemplate, sampleData: StructuredLetterData): {
  success: boolean;
  error?: string;
  warnings?: string[];
  htmlLength?: number;
} {
  try {
    const html = fillLetterTemplate(template, sampleData);
    const templateData = convertToLetterTemplateData(sampleData);
    const validation = validateLetterTemplateDataWithDetails(templateData);
    
    return {
      success: true,
      warnings: validation.warnings,
      htmlLength: html.length
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get available letter templates count
 */
export function getLetterTemplateCount(): number {
  return precompiledLetterTemplates.size;
}

/**
 * Check if a template is cached
 */
export function isLetterTemplateCached(templateId: string): boolean {
  return precompiledLetterTemplates.has(templateId);
}

/**
 * Generate letter content from plain text
 * This utility helps convert plain text letters to structured format
 */
export function parseLetterFromPlainText(plainText: string, jobInfo: { position: string; company: string }): StructuredLetterData {
  const lines = plainText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  
  // Extract basic information
  const today = new Date().toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });

  // Simple parsing logic - this can be enhanced
  const bodyParagraphs = lines.filter(line => 
    !line.includes('Hormat saya') && 
    !line.includes('Yth.') &&
    !line.includes('Perihal:') &&
    line.length > 20
  );

  return {
    personalInfo: {
      fullName: 'Nama Pelamar', // This would be extracted from context
      email: '',
      phone: '',
      location: ''
    },
    jobInfo: {
      position: jobInfo.position,
      company: jobInfo.company
    },
    letterContent: {
      date: today,
      subject: `Lamaran Pekerjaan sebagai ${jobInfo.position}`,
      salutation: 'Yth. Bapak/Ibu HRD',
      opening: bodyParagraphs[0] || '',
      body: bodyParagraphs.slice(1, -1),
      closing: bodyParagraphs[bodyParagraphs.length - 1] || '',
      signature: 'Hormat saya'
    },
    metadata: {
      generatedAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      letterType: 'application',
      aiGenerated: true
    }
  };
}
